{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"sw-backoffice": {"root": "", "sourceRoot": "src", "projectType": "application", "prefix": "", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"allowedCommonJsDependencies": ["moment-timezone", "dragula", "angular2-text-mask", "validator/lib/isEmail", "dom-autoscroller"], "outputPath": {"base": "dist"}, "index": "src/index.html", "polyfills": ["src/polyfills.ts"], "tsConfig": "src/tsconfig.app.json", "assets": [{"glob": "**/*", "input": "src/assets/static", "output": "/"}, "src/locale"], "styles": ["src/app/theme/theme.less", "src/styles.scss"], "scripts": [], "baseHref": "/", "extractLicenses": false, "sourceMap": true, "optimization": false, "namedChunks": true, "browser": "src/main.ts"}, "configurations": {"development": {"aot": false, "budgets": [{"type": "anyComponentStyle", "maximumWarning": "6kb"}], "outputHashing": "all"}, "production": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb"}]}}, "defaultConfiguration": ""}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"proxyConfig": "proxy.conf.js", "port": 3001, "hmr": false, "watch": true, "buildTarget": "sw-backoffice:build:development"}, "configurations": {"development": {"buildTarget": "sw-backoffice:build:development"}, "production": {"buildTarget": "sw-backoffice:build:production"}}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "sw-backoffice:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "src/tsconfig.spec.json", "karmaConfig": "src/karma.conf.js", "styles": ["src/app/theme/theme.less"], "scripts": [], "assets": [{"glob": "**/*", "input": "src/assets/static", "output": "/"}, "src/locale"], "codeCoverage": true}}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["src/**/*.ts", "src/**/*.html"]}}}}}, "cli": {"analytics": false}}