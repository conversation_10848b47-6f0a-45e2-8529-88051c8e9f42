import { Component, Inject } from '@angular/core';
import { LobbyWidget } from '../../../../../../../common/services/lobby-widgets.service';
import { LobbyMenuItemWidgetOptions } from '../../../../../lobby.model';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { FormGroup, ValidationErrors } from '@angular/forms';
import { DynamicFormOptionData } from '@skywind-group/lib-swui';

function jsonReplacer( k, v: any ) {
  if (k === '') {
    return v;
  }
  return v === null ? undefined : v;
}

function jsonStringify( value: any ): string {
  return JSON.stringify(value, jsonReplacer);
}

export interface WidgetOptionsDialogData {
  widget: LobbyWidget;
  options?: LobbyMenuItemWidgetOptions;
}

@Component({
    templateUrl: 'widget-options-dialog.component.html',
    standalone: false
})
export class WidgetOptionsDialogComponent {
  readonly form = new FormGroup({}, {
    validators: ( control: FormGroup ): ValidationErrors | null => {
      const value = control.getRawValue();
      if (value) {
        try {
          jsonStringify(value);
        } catch {
          return { jsonInvalid: true };
        }
      }
      return null;
    }
  });
  readonly options?: DynamicFormOptionData;

  constructor( private readonly dialogRef: MatDialogRef<WidgetOptionsDialogComponent>,
               @Inject(MAT_DIALOG_DATA) { widget, options }: WidgetOptionsDialogData ) {
    this.options = widget.properties && Object.entries(widget.properties).reduce(( result, [key, prop] ) => ({
      ...result,
      [key]: {
        ...prop,
        value: options[key]
      }
    }), {});
  }

  saveChanges( event: Event ) {
    event.preventDefault();
    this.dialogRef.close(jsonStringify(this.form.getRawValue()));
  }
}
