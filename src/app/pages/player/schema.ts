import { SchemaFilterMatchEnum, SwuiGridField } from '@skywind-group/lib-swui';
import { SwHubEntityItem } from '@skywind-group/lib-swui';
import { COUNTRY_LIST$, CURRENCY_LIST$, LANGUAGES_LIST$ } from '../../app.constants';
import { PagesRoutes } from '../pages.menu';
import { transformCurrencyItem, transformFormatCurrencyValue } from '../../common/core/currecy-transform';
import { map } from 'rxjs/operators';

export const SCHEMA: SwuiGridField[] = [
  {
    field: 'brandTitle',
    title: 'CUSTOMERS.GRID.operator',
    type: 'string',
    dataSource: '',
    isList: true,
    isViewable: true,
    isSortable: false,
    isFilterable: false,
    td: {
      type: 'string',
      nowrap: true
    },
    alignment: {
      th: 'left',
      td: 'left',
    },
  },
  {
    field: 'agentDomain',
    title: 'CUSTOMERS.GRID.agentDomain',
    type: 'string',
    dataSource: '',
    isList: false,
    isViewable: true,
    isSortable: false,
    isFilterable: false,
    scope: 'ALL.DEFAULT_SCHEMA_SECTIONS.operatorAgent',
  },
  {
    field: 'code',
    title: 'CUSTOMERS.GRID.code',
    type: 'string',
    isList: true,
    isViewable: true,
    isSortable: true,
    isFilterable: true,
    td: {
      type: 'link',
      data: [],
      linkFn: (row, schema) => {
        const fullPath = (schema.td.data as SwHubEntityItem[]).find(entity => entity.id === row.brandId)?.path;

        if (!fullPath) {
          return [row.code];
        }

        let url;
        if (fullPath && fullPath !== ':') {
          url = `/pages/${PagesRoutes.Customers}/${fullPath}/${row.code}`;
        } else {
          url = `/pages/${PagesRoutes.Customers}/${row.code}`;
        }
        return [url];
      },
      titleFn: (row) => row.code,
      isDisabled: (row, schema) => !((schema.td as any).data as SwHubEntityItem[]).find(entity => entity.id === row.brandId)?.path
    },
    filterMatch: SchemaFilterMatchEnum.Equals,
    alignment: {
      th: 'left',
      td: 'left',
    },
  },
  {
    field: 'login',
    title: 'CUSTOMERS.GRID.login',
    type: 'string',
    isList: false,
    isViewable: true,
    isSortable: false,
    isFilterable: false,
    filterMatch: SchemaFilterMatchEnum.Equals,
  },
  {
    field: 'firstName',
    title: 'CUSTOMERS.GRID.firstName',
    type: 'string',
    isList: true,
    isViewable: true,
    isSortable: true,
    isFilterable: true,
    filterMatch: SchemaFilterMatchEnum.Contains,
    td: {
      type: 'string',
      nowrap: true
    },
    alignment: {
      th: 'left',
      td: 'left',
    },
  },
  {
    field: 'lastName',
    title: 'CUSTOMERS.GRID.lastName',
    type: 'string',
    isList: true,
    isViewable: true,
    isSortable: true,
    isFilterable: true,
    filterMatch: SchemaFilterMatchEnum.Contains,
    td: {
      type: 'string',
      nowrap: true
    },
    alignment: {
      th: 'left',
      td: 'left',
    },
  },
  {
    field: 'email',
    title: 'CUSTOMERS.GRID.email',
    type: 'string',
    isList: false,
    isViewable: false,
    isSortable: false,
    isFilterable: true,
    filterMatch: SchemaFilterMatchEnum.Equals,
  },
  {
    field: 'language',
    title: 'CUSTOMERS.GRID.language',
    type: 'select',
    data: LANGUAGES_LIST$,
    isList: false,
    isViewable: false,
    isSortable: false,
    isFilterable: true,
    filterMatch: SchemaFilterMatchEnum.In,
  },
  {
    field: 'currency',
    title: 'CUSTOMERS.GRID.currency',
    type: 'multiselect',
    search: {
      show: true,
      placeholder: 'Search'
    },
    isList: true,
    isViewable: true,
    isSortable: false,
    isFilterable: true,
    // dataSource: 'currency',
    data: CURRENCY_LIST$.pipe(map((currs) => currs.map(({displayName, code}) => ({id: code, text: displayName})))),
    scope: 'ALL.DEFAULT_SCHEMA_SECTIONS.balance',
    filterMatch: SchemaFilterMatchEnum.In,
    td: {
      type: 'calc',
      titleFn: row => transformCurrencyItem(0, row.currency).label
    },
    alignment: {
      th: 'center',
      td: 'center',
    },
  },
  {
    field: 'balance',
    title: 'CUSTOMERS.GRID.balance',
    type: 'number',
    isList: true,
    isViewable: true,
    isSortable: false,
    isFilterable: false,
    scope: 'ALL.DEFAULT_SCHEMA_SECTIONS.balance',
    td: {
      type: 'calc',
      titleFn: (row: any, schema: SwuiGridField) => {
        return transformFormatCurrencyValue(Number(row[schema.field]), row.currency);
      }
    },
    alignment: {
      th: 'right',
      td: 'right',
    }
  },
  {
    field: 'balances',
    title: 'CUSTOMERS.GRID.balances',
    type: 'array',
    isList: false,
    isViewable: true,
    isSortable: true,
    isFilterable: false,
    scope: 'ALL.DEFAULT_SCHEMA_SECTIONS.balance',
    td: {
      type: 'string',
      nowrap: true
    },
    alignment: {
      th: 'right',
      td: 'right',
    },
  },
  {
    field: 'provider',
    title: 'CUSTOMERS.GRID.provider',
    type: 'string',
    isList: false,
    isViewable: true,
    isSortable: false,
    isFilterable: false,
  },
  {
    field: 'status',
    title: 'CUSTOMERS.GRID.status',
    type: 'select',
    emptyOption: {
      show: true,
      placeholder: '- All -'
    },
    isList: true,
    data: [
      {id: 'normal', code: 'normal', displayName: 'CUSTOMERS.LABEL.userActive'},
      {id: 'suspended', code: 'suspended', displayName: 'CUSTOMERS.LABEL.userInactive'}
    ],
    isViewable: true,
    isSortable: true,
    isFilterable: true,
    td: {
      type: 'calc',
      titleFn: (row: any) =>
        row && row.status === 'normal' ? 'CUSTOMERS.LABEL.userActive' : 'CUSTOMERS.LABEL.userInactive',
      classFn: (row: any) => {
        return row.status === 'normal' ? 'sw-chip sw-chip-green' : (
          row.status === 'suspended' ? 'sw-chip' : ''
        );
      }
    },
    alignment: {
      th: 'center',
      td: 'center',
    },
  },
  {
    field: 'isTest',
    title: 'CUSTOMERS.GRID.isTest',
    type: 'string',
    isList: true,
    placeholder: 'type',
    isViewable: true,
    isSortable: false,
    isFilterable: false,
    td: {
      type: 'calc',
      titleFn: (row: any) =>
        row && row.isTest ? 'CUSTOMERS.LABEL.testAccount' : 'CUSTOMERS.LABEL.realAccount',
      classFn: (row: any) => row.isTest ? 'sw-chip' : 'sw-chip sw-chip-success',
    },
    alignment: {
      th: 'center',
      td: 'center',
    },
  },
  {
    field: 'createdAt',
    title: 'CUSTOMERS.GRID.createdAt',
    type: 'datetimerange',
    dataSource: '',
    isList: true,
    isViewable: true,
    isSortable: true,
    isFilterable: true,
    scope: 'ALL.DEFAULT_SCHEMA_SECTIONS.dateTime',
    config: {
      timePicker: true,
    },
    filterMatch: {
      from: SchemaFilterMatchEnum.GreaterThanEquals,
      to: SchemaFilterMatchEnum.LessThanEquals,
    },
    td: {
      type: 'timestamp',
      nowrap: true
    },
    alignment: {
      th: 'right',
      td: 'right',
    },
  },
  {
    field: 'lastLogin',
    title: 'CUSTOMERS.GRID.lastLogin',
    type: 'datetimerange',
    isList: true,
    isViewable: true,
    isSortable: true,
    isFilterable: true,
    scope: 'ALL.DEFAULT_SCHEMA_SECTIONS.dateTime',
    config: {
      timePicker: true,
    },
    filterMatch: {
      from: SchemaFilterMatchEnum.GreaterThanEquals,
      to: SchemaFilterMatchEnum.LessThanEquals,
    },
    td: {
      type: 'timestamp',
      nowrap: true
    },
    alignment: {
      th: 'right',
      td: 'right',
    },
  },
  {
    field: 'country',
    title: 'CUSTOMERS.GRID.country',
    type: 'multiselect',
    search: {
      show: true,
      placeholder: 'Search'
    },
    isList: true,
    isViewable: true,
    isSortable: false,
    isFilterable: true,
    data: COUNTRY_LIST$,
    scope: 'ALL.DEFAULT_SCHEMA_SECTIONS.other',
    filterMatch: SchemaFilterMatchEnum.In,
    alignment: {
      th: 'center',
      td: 'center',
    },
  },
  {
    field: 'registrationIP',
    title: 'CUSTOMERS.GRID.registrationIP',
    type: 'string',
    isList: false,
    isViewable: false,
    isSortable: false,
    isFilterable: false,
    scope: 'ALL.DEFAULT_SCHEMA_SECTIONS.other',
    td: {
      type: 'string',
      nowrap: true
    },
    alignment: {
      th: 'left',
      td: 'left',
    },
  },
];

export const SCHEMA_FILTER = SCHEMA.filter(el => el.isFilterable);
export const SCHEMA_LIST = SCHEMA.filter(el => el.isList);
