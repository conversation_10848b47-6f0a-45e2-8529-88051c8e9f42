import { Schema<PERSON><PERSON><PERSON><PERSON><PERSON>Enum, SwuiGridField } from '@skywind-group/lib-swui';
import { SwHubEntityItem } from '@skywind-group/lib-swui';
import { CURRENCY_LIST$ } from '../../../../app.constants';
import { CurrencyModel } from '../../../../common/models/currency.model';
import { FINISHED_CLASS_MAP, ORDER_STATUS_LIST } from './base-payments.model';
import { getCurrencyDivider, transformCurrencyItem, transformFormatCurrencyValue } from '../../../../common/core/currecy-transform';
import { map } from 'rxjs/operators';

export const TRANSFERS_SCHEMA: SwuiGridField[] = [
  {
    field: 'cpTransferId',
    title: 'PAYMENTS_TRANSFERS.GRID.cpTransferId',
    type: 'number',
    isList: false,
    isViewable: false,
    isSortable: false,
    isFilterable: false,
    scope: 'ALL.DEFAULT_SCHEMA_SECTIONS.basic',
  },
  {
    field: 'trxId',
    title: 'PAYMENTS_TRANSFERS.GRID.trxId',
    type: 'string',
    isList: true,
    isViewable: true,
    isSortable: true,
    isFilterable: true,
    scope: 'ALL.DEFAULT_SCHEMA_SECTIONS.basic',
    td: {
      type: 'string',
      nowrap: true
    },
    alignment: {
      th: 'left',
      td: 'left',
    },
  },
  {
    field: 'playerCode',
    title: 'PAYMENTS_TRANSFERS.GRID.playerCode',
    type: 'string',
    isList: true,
    isViewable: true,
    isSortable: true,
    isFilterable: true,
    td: {
      type: 'link',
      data: [],
      linkFn: ( row, schema ) => {
        const fullPath = (schema.td.data as SwHubEntityItem[]).find(entity => entity.id === row.brandId).path;
        let url;
        if (fullPath && fullPath !== ':') {
          url = `/pages/players/${fullPath}/${row.playerCode}`;
        } else {
          url = `/pages/players/${row.playerCode}`;
        }
        return [url];
      },
      titleFn: ( row ) => row.playerCode,
    },
    filterMatch: SchemaFilterMatchEnum.Equals,
    alignment: {
      th: 'left',
      td: 'left',
    },
  },
  {
    field: 'from',
    title: 'PAYMENTS_TRANSFERS.GRID.transferSource',
    type: 'date',
    isList: true,
    isViewable: true,
    isSortable: false,
    isFilterable: false,
    scope: 'ALL.DEFAULT_SCHEMA_SECTIONS.basic',
    td: {
      type: 'string',
      nowrap: true
    },
    alignment: {
      th: 'right',
      td: 'right',
    },
  },
  {
    field: 'extTrxId',
    title: 'PAYMENTS_TRANSFERS.GRID.extTrxId',
    type: 'string',
    isList: true,
    isViewable: true,
    isSortable: false,
    isFilterable: true,
    scope: 'ALL.DEFAULT_SCHEMA_SECTIONS.basic',
    td: {
      type: 'string',
      nowrap: true
    },
    alignment: {
      th: 'left',
      td: 'left',
    },
  },
  {
    field: 'amount',
    title: 'PAYMENTS_TRANSFERS.GRID.amount',
    type: 'numericrange',
    isList: true,
    isViewable: true,
    isSortable: true,
    isFilterable: true,
    dependsOn: 'currencyCode',
    dependsOnFieldName: 'PAYMENTS_TRANSFERS.GRID.currencyCode',
    getDivider: getCurrencyDivider,
    filterMatch: {
      from: SchemaFilterMatchEnum.GreaterThanEquals,
      to: SchemaFilterMatchEnum.LessThanEquals,
    },
    scope: 'ALL.DEFAULT_SCHEMA_SECTIONS.financial',
    td: {
      type: 'calc',
      titleFn: row => transformFormatCurrencyValue(row.amount, row.currencyCode)
    },
    alignment: {
      th: 'right',
      td: 'right',
    }
  },
  {
    field: 'playerBalanceAfter',
    title: 'PAYMENTS_TRANSFERS.GRID.playerBalanceAfter',
    type: 'numericrange',
    isList: true,
    isViewable: true,
    isSortable: true,
    isFilterable: true,
    dependsOn: 'currencyCode',
    dependsOnFieldName: 'PAYMENTS_TRANSFERS.GRID.currencyCode',
    getDivider: getCurrencyDivider,
    filterMatch: {
      from: SchemaFilterMatchEnum.GreaterThanEquals,
      to: SchemaFilterMatchEnum.LessThanEquals,
    },
    scope: 'ALL.DEFAULT_SCHEMA_SECTIONS.financial',
    td: {
      type: 'calc',
      titleFn: row => transformFormatCurrencyValue(row.playerBalanceAfter, row.currencyCode)
    },
    alignment: {
      th: 'right',
      td: 'right',
    }
  },
  {
    field: 'currencyCode',
    title: 'PAYMENTS_TRANSFERS.GRID.currencyCode',
    type: 'select',
    search: {
      show: true,
      placeholder: 'Search'
    },
    isList: true,
    isViewable: true,
    isSortable: true,
    isFilterable: true,
    data: CURRENCY_LIST$.pipe(map(curr => curr.map((c: any) => (new CurrencyModel(c)).toSelectOption()))),
    scope: 'ALL.DEFAULT_SCHEMA_SECTIONS.financial',
    alignment: {
      th: 'center',
      td: 'center',
    },
    td: {
      type: 'calc',
      titleFn: row => transformCurrencyItem(0, row.currencyCode).label
    }
  },
  {
    field: 'remainingBalance',
    title: 'PAYMENTS_TRANSFERS.GRID.remainingBalance',
    type: 'string',
    isList: false,
    isViewable: false,
    isSortable: false,
    isFilterable: false,
    scope: 'ALL.DEFAULT_SCHEMA_SECTIONS.financial',
    td: {
      type: 'calc',
      titleFn: row => transformFormatCurrencyValue(row.remainingBalance, row.currencyCode)
    },
  },
  {
    field: 'to',
    title: 'PAYMENTS_TRANSFERS.GRID.transferDest',
    type: 'date',
    isList: true,
    isViewable: true,
    isSortable: false,
    isFilterable: false,
    scope: 'ALL.DEFAULT_SCHEMA_SECTIONS.basic',
    td: {
      type: 'string',
      nowrap: true
    },
    alignment: {
      th: 'right',
      td: 'right',
    },
  },
  {
    field: 'updatedBalance',
    title: 'PAYMENTS_TRANSFERS.GRID.updatedBalance',
    type: 'string',
    isList: false,
    isViewable: false,
    isSortable: false,
    isFilterable: false,
    scope: 'ALL.DEFAULT_SCHEMA_SECTIONS.financial',
    td: {
      type: 'calc',
      titleFn: row => transformFormatCurrencyValue(row.updatedBalance, row.currencyCode)
    },
  },
  {
    field: 'startDate',
    title: 'PAYMENTS_TRANSFERS.GRID.startDate',
    type: 'datetimerange',
    isList: true,
    isViewable: true,
    isSortable: true,
    isFilterable: true,
    scope: 'ALL.DEFAULT_SCHEMA_SECTIONS.dateTime',
    config: {
      timePicker: true,
    },
    filterMatch: {
      from: SchemaFilterMatchEnum.GreaterThanEquals,
      to: SchemaFilterMatchEnum.LessThanEquals,
    },
    td: {
      type: 'timestamp',
      nowrap: true
    },
    alignment: {
      th: 'right',
      td: 'right',
    },
  },
  {
    field: 'endDate',
    title: 'PAYMENTS_TRANSFERS.GRID.endDate',
    type: 'datetimerange',
    isList: true,
    isViewable: true,
    isSortable: true,
    isFilterable: true,
    scope: 'ALL.DEFAULT_SCHEMA_SECTIONS.dateTime',
    config: {
      timePicker: true,
    },
    filterMatch: {
      from: SchemaFilterMatchEnum.GreaterThanEquals,
      to: SchemaFilterMatchEnum.LessThanEquals,
    },
    td: {
      type: 'timestamp',
      nowrap: true
    },
    alignment: {
      th: 'right',
      td: 'right',
    },
  },
  {
    field: 'orderStatus',
    title: 'PAYMENTS_TRANSFERS.GRID.orderStatus',
    type: 'boolean',
    isList: true,
    isViewable: true,
    isSortable: false,
    isFilterable: false,
    td: {
      type: 'calc',
      optionsList: ORDER_STATUS_LIST,
      classMap: {
        true: 'sw-chip sw-chip-success',
        false: 'sw-chip',
      },
      titleFn: ( row: any, schema: SwuiGridField ) => {
        let dataItem = ORDER_STATUS_LIST.find(item => item.id === row[schema.field]);
        return (dataItem && dataItem.title);
      },
      classFn: ( row: any, schema: SwuiGridField ) => FINISHED_CLASS_MAP[(row && row[schema.field])]
    },
    alignment: {
      th: 'center',
      td: 'center',
    },
  }
];
