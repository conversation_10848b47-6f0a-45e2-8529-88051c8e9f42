export class RgReportModel {
  _meta: object = {};

  playerCode: string;
  brandId: string;
  productType: string;
  suspensionTypes: string[];
  createdAt: string;
  endTime: string;

  constructor( obj ) {
    this.playerCode = obj && obj.playerCode || '';
    this.brandId = obj && obj.brandId || '';
    this.productType = obj && obj.productType || '';
    this.suspensionTypes = obj && obj.suspensionTypes || '';
    this.createdAt = obj && obj.createdAt || '';
    this.endTime = obj && obj.endTime || '';
  }
}

export const SUSPENSION_TYPES = [
  { id: 'time-out', displayName: 'REPORT_RG.GRID.suspension_type_timeout' },
  { id: 'self-exclusion', displayName: 'REPORT_RG.GRID.suspension_type_exclusion' }
];

export const SUSPENSION_TYPES_FILTER = [
  { id: 'time-out', displayName: 'Timeout' },
  { id: 'self-exclusion', displayName: 'Self-exclusion' }
];

export const PRODUCT_TYPES = [
  { id: 'casino', code: 'casino', displayName: 'REPORT_RG.GRID.product_type_casino' },
  { id: 'sports', code: 'sports', displayName: 'REPORT_RG.GRID.product_type_sports' }
];

export const PRODUCT_TYPES_CLASS_MAP = {
  casino: 'label-casino',
  sports: 'label-sports',
};

export const SUSPENSION_TYPE_MAP = {
  'time-out': 'REPORT_RG.GRID.suspension_type_timeout',
  'self-exclusion': 'REPORT_RG.GRID.suspension_type_exclusion'
};
