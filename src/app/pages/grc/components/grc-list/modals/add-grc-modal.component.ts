import { Component, Inject } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { AppSettings, SettingsService, SwuiDatePickerConfig } from '@skywind-group/lib-swui';
import { SwuiDateTimepickerConfig } from '@skywind-group/lib-swui';
import moment from 'moment';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { ErrorMessage } from '../../../../../common/components/mat-user-editor/user-form.component';
import { CurrencyModel } from '../../../../../common/models/currency.model';
import { SelectOptionModel } from '../../../../../common/models/select-option.model';
import { ValidationService } from '../../../../../common/services/validation.service';

import { Currency } from '../../../../../common/typings';
import { MESSAGE_ERROR } from '../../../grc-errors.constants';

@Component({
    selector: 'add-grc-modal',
    templateUrl: 'add-grc-modal.component.html',
    styleUrls: ['../../../grc.component.scss'],
    standalone: false
})
export class AddGrcModalComponent {
  public addForm: FormGroup;
  public currencies: Currency[] = [];
  public currencySelectOptions: SelectOptionModel[] = [];
  public messageErrors: ErrorMessage = MESSAGE_ERROR;
  public minCalendarDate = moment().toISOString();
  readonly config$: Observable<SwuiDateTimepickerConfig>;

  constructor( private fb: FormBuilder,
               public dialogRef: MatDialogRef<AddGrcModalComponent>,
               { appSettings$ }: SettingsService,
               @Inject(MAT_DIALOG_DATA) data: any
  ) {
    this.currencies = data.currencies;

    this.config$ = appSettings$.pipe(
      map<AppSettings, SwuiDatePickerConfig>(( { timezoneName } ) => ({
        timeZone: timezoneName,
        timePicker: true
      }))
    );
  }

  ngOnInit() {
    this.buildCurrencySelectOptions();
    this.addForm = this.initForm();
  }

  public buildCurrencySelectOptions() {
    this.currencySelectOptions = this.currencies.map(
      ( currency: any ) => (new CurrencyModel(currency)).toSelectOption()
    );
  }

  public initForm(): FormGroup {
    return this.fb.group({
      name: [
        '', Validators.compose([
          Validators.required,
          ValidationService.maxLength(255)
        ])
      ],
      startAt: [
        '', Validators.compose([
          Validators.required,
          ValidationService.dateGreaterThanNow
        ])
      ],
      finishAt: [
        '', Validators.compose([
          Validators.required,
          ValidationService.dateGreaterThanNow
        ])
      ],
      potAmount: [
        '', Validators.compose([
          Validators.required,
          Validators.min(1)
        ])
      ],
      targetBadges: [
        '', Validators.compose([
          Validators.required,
          Validators.min(1),
          Validators.max(2147483646)
        ])
      ],
      currency: ['', Validators.required],
      definition: this.fb.group({
        logoUrl: ['', ValidationService.IfNotEmpty(ValidationService.urlValidation)]
      })
    }, {
      validator: ValidationService.startAndEndDate('startAt', 'finishAt'),
    });
  }

  get addFormGroup(): FormGroup {
    return this.addForm as FormGroup;
  }

  get definitionGroup(): FormGroup {
    return this.addFormGroup.get('definition') as FormGroup;
  }

  get nameControl(): FormControl {
    return this.addFormGroup.get('name') as FormControl;
  }

  get startAtControl(): FormControl {
    return this.addFormGroup.get('startAt') as FormControl;
  }

  get finishAtControl(): FormControl {
    return this.addFormGroup.get('finishAt') as FormControl;
  }

  get potAmountControl(): FormControl {
    return this.addFormGroup.get('potAmount') as FormControl;
  }

  get currencyControl(): FormControl {
    return this.addFormGroup.get('currency') as FormControl;
  }

  get logoUrlControl(): FormControl {
    return this.definitionGroup.get('logoUrl') as FormControl;
  }

  get targetBadgesControl(): FormControl {
    return this.addFormGroup.get('targetBadges') as FormControl;
  }

  onNoClick() {
    this.dialogRef.close(null);
  }

  onConfirmClick() {
    this.dialogRef.close(this.addForm.value);
  }
}
