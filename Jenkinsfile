pipeline {
  agent any
  options {
    disableConcurrentBuilds()
    timestamps()
    timeout(time: 1, unit: 'HOURS')
  }
  environment {
    REVISION = sh(returnStdout: true, script: 'git rev-parse HEAD').trim()
    BRANCH_NAME_NORMALIZED = "${BRANCH_NAME.toLowerCase().replace("/", "_")}"
    REPO = "skywindgroup"
    NAME = "ubo-hub-casino"
    SERVICE = "sw-${NAME}"
    REGION = "asia.gcr.io"
    PROJECT = "gcpstg"
    NODE = "jod"
  }

  stages {
    stage('NPM install') {
      when {
        anyOf {
          branch 'develop'
          branch 'release/*'
          branch 'feature/*'
          expression { env.BRANCH_NAME.startsWith('PR-') }
        }
      }
      steps {
        withDockerContainer(image: "node:${NODE}", toolName: 'latest') {
          configFileProvider([configFile(fileId: 'npm-npmjs', targetLocation: '.npmrc')]) {
            sh 'touch .env'
            sh 'npm ci --verbose --legacy-peer-deps'
          }
        }
      }
    }
    stage('Lint') {
      when {
        expression { env.BRANCH_NAME.startsWith('PR-') }
      }
      steps {
        withDockerContainer(image: "node:${NODE}", toolName: 'latest') {
          configFileProvider([configFile(fileId: 'npm-npmjs', targetLocation: '.npmrc')]) {
            sh 'npm run lint'
          }
        }
      }
    }
    stage('Test/unit') {
      when {
        expression { env.BRANCH_NAME.startsWith('PR-') }
      }
      steps {
        withDockerContainer(image: "circleci/node:${NODE}-browsers", toolName: 'latest') {
          configFileProvider([configFile(fileId: 'npm-npmjs', targetLocation: '.npmrc')]) {
            sh 'npm run ci:test'
          }
        }
      }
    }
    stage('Build') {
      when {
        anyOf {
          branch 'develop'
          branch 'release/*'
          branch 'feature/*'
          expression { env.BRANCH_NAME.startsWith('PR-') }
        }
      }
      steps {
        withDockerContainer(image: "node:${NODE}", toolName: 'latest') {
          configFileProvider([configFile(fileId: 'npm-npmjs', targetLocation: '.npmrc')]) {
            sh 'npm run config'
            sh 'npm run build:prod'
          }
        }
      }
    }
    stage('Build Docker image') {
      when {
        anyOf {
          branch 'develop'
          branch 'release/*'
          branch 'feature/*'
        }
      }
      steps {
        sh "docker build --no-cache -f Dockerfile -t ${REPO}/${SERVICE}:${BRANCH_NAME_NORMALIZED} ."
      }
    }
    stage('Tag image') {
      when {
        anyOf {
          branch 'develop'
          branch 'release/*'
          branch 'feature/*'
        }
      }
      steps {
        sh 'docker tag ${REPO}/${SERVICE}:${BRANCH_NAME_NORMALIZED} ${REGION}/${PROJECT}/${SERVICE}:${BRANCH_NAME_NORMALIZED}'
        sh 'docker tag ${REPO}/${SERVICE}:${BRANCH_NAME_NORMALIZED} ${REGION}/${PROJECT}/${SERVICE}:${REVISION}'
      }
    }
    stage('Push image') {
      when {
        anyOf {
          branch 'develop'
          branch 'release/*'
          branch 'feature/*'
        }
      }
      steps {
        sh 'docker push ${REPO}/${SERVICE}:${BRANCH_NAME_NORMALIZED}'
        sh 'docker push ${REGION}/${PROJECT}/${SERVICE}:${BRANCH_NAME_NORMALIZED}'
        sh 'docker push ${REGION}/${PROJECT}/${SERVICE}:${REVISION}'
      }
    }
    stage('Deploy GMS') {
      when {
        anyOf {
          branch 'develop'
          branch 'release/*'
          branch 'feature/*'
        }
      }
      steps {
        withDockerContainer(image: "gcr.io/google.com/cloudsdktool/cloud-sdk", toolName: 'latest') {
          withCredentials([file(credentialsId: 'ro-dev-qa', variable: 'KUBECONFIG')]) {
            sh "kubectl rollout restart deployment/${NAME} -n=gms"
          }
        }
      }
    }
    stage('Deploy QA') {
      when {
        branch 'release/*'
        branch 'feature/*'
      }
      steps {
        withDockerContainer(image: "gcr.io/google.com/cloudsdktool/cloud-sdk", toolName: 'latest') {
          withCredentials([file(credentialsId: 'ro-dev-qa', variable: 'KUBECONFIG')]) {
            sh "kubectl rollout restart deployment/${NAME} -n=qa"
          }
        }
      }
    }
  }
}


