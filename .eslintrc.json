{
  "root": true,
  "ignorePatterns": [
    "**/node_modules/**",
    "src/vendor/**/**.ts",
    "src/vendor/**/**.html"
  ],
  "overrides": [
    {
      "files": [
        "*.ts"
      ],
      "parser": "@typescript-eslint/parser",
      "parserOptions": {
        "project": [
          "tsconfig.json",
          "src/tsconfig.app.json",
          "src/tsconfig.spec.json"
        ],
        "createDefaultProgram": true
      },
      "plugins": [
        "@stylistic"
      ],
      "extends": [
        "eslint:recommended",
        "plugin:@typescript-eslint/recommended",
        "plugin:@angular-eslint/recommended",
        "plugin:@angular-eslint/template/process-inline-templates",
        "plugin:import/recommended",
        "plugin:import/typescript"
      ],
      "rules": {
        "@angular-eslint/component-class-suffix": "warn", // "error"
        "@angular-eslint/component-selector": [
          "warn", // "error"
          {
            "type": "element",
            "prefix": "app",
            "style": "kebab-case"
          }
        ],
        "@angular-eslint/directive-class-suffix": "warn", // "error"
        "@angular-eslint/directive-selector": [
          "warn", // "error"
          {
            "type": "attribute",
            "prefix": "app",
            "style": "camelCase"
          }
        ],
        "@angular-eslint/no-conflicting-lifecycle": "warn", // "error"
        "@angular-eslint/no-host-metadata-property": "warn", // "error"
        "@angular-eslint/no-input-rename": "warn", // "error"
        "@angular-eslint/no-inputs-metadata-property": "warn", // "error"
        "@angular-eslint/no-output-native": "warn", // "error"
        "@angular-eslint/no-output-on-prefix": "warn", // "error",
        "@angular-eslint/no-output-rename": "warn", // "error"
        "@angular-eslint/no-outputs-metadata-property": "warn", // "error"
        "@angular-eslint/use-lifecycle-interface": "warn", // "error"
        "@angular-eslint/use-pipe-transform-interface": "warn", // "error"
        "@angular-eslint/no-empty-lifecycle-methods": "warn", // "error"
        "@typescript-eslint/consistent-type-definitions": "warn", // "error"
        "@typescript-eslint/dot-notation": "off",
        "@typescript-eslint/explicit-member-accessibility": [
          "off",
          {
            "accessibility": "explicit"
          }
        ],
        "@typescript-eslint/member-ordering": [
          "warn", // "error"
          {
            "default": [
              "public-static-field",
              "private-static-field",
              "public-instance-field",
              "private-instance-field",
              "public-constructor",
              "private-constructor",
              "public-instance-method",
              "protected-instance-method",
              "private-instance-method"
            ]
          }
        ],
        "@typescript-eslint/no-empty-function": "off",
        "@typescript-eslint/no-empty-interface": "warn", // "error"
        "@typescript-eslint/no-inferrable-types": "off",
        "@typescript-eslint/no-misused-new": "warn", // "error"
        "@typescript-eslint/no-non-null-assertion": "warn", // "error"
        "@typescript-eslint/no-shadow": [
          "warn", // "error"
          {
            "hoist": "all"
          }
        ],
        "@typescript-eslint/no-unused-expressions": "warn", // "error"
        "@typescript-eslint/no-var-requires": "off",
        "@typescript-eslint/prefer-function-type": "warn", // "error"
        "@typescript-eslint/no-require-imports": "warn", // "error"
        "@stylistic/quotes": [
          "warn", // "error"
          "single",
          {
            "avoidEscape": true
          }
        ],
        "@stylistic/semi": [
          "warn", // "error"
          "always"
        ],
        "@stylistic/type-annotation-spacing": "warn", // "error"
        "@typescript-eslint/unified-signatures": "warn", // "error"
        "@typescript-eslint/no-explicit-any": "off", // "error"
        "arrow-body-style": "warn", // "error"
        "@stylistic/brace-style": [
          "warn", // "error"
          "1tbs"
        ],
        "constructor-super": "warn", // "error"
        "curly": "off",
        "@stylistic/eol-last": "warn", // "error"
        "eqeqeq": [
          "warn", // "error"
          "smart"
        ],
        "guard-for-in": "warn", // "error"
        "id-blacklist": "off",
        "id-match": "off",
        "import/no-deprecated": "warn", // "error"
        "import/no-unresolved": "warn", // "error"
        "@stylistic/indent": [
          "warn", // "error"
          2
        ],
        "@stylistic/max-len": [
          "warn", // "error"
          {
            "code": 140
          }
        ],
        "no-bitwise": "warn", // "error"
        "no-caller": "warn", // "error"
        "no-console": [
          "warn", // "error"
          {
            "allow": [
              "warn",
              "error"
            ]
          }
        ],
        "no-debugger": "warn", // "error"
        "no-empty": "off",
        "no-eval": "warn", // "error"
        "no-fallthrough": "warn", // "error"
        "no-new-wrappers": "warn", // "error"
        "no-restricted-imports": [
          "warn", // "error"
          "rxjs/Rx"
        ],
        "no-throw-literal": "warn", // "error"
        "@stylistic/no-trailing-spaces": "warn", // "error"
        "no-undef-init": "warn", // "error"
        "no-underscore-dangle": "off",
        "no-unused-labels": "warn", // "error"
        "no-var": "warn", // "error"
        "prefer-const": "off", // "error"
        "radix": "error",
        "no-prototype-builtins": "warn", // "error"
        "@stylistic/spaced-comment": [
          "warn", // "error"
          "always",
          {
            "markers": [
              "/"
            ]
          }
        ]
      }
    },
    {
      "files": [
        "*.html"
      ],
      "extends": [
        "plugin:@angular-eslint/template/recommended",
        "plugin:@angular-eslint/template/accessibility"
      ],
      "rules": {}
    }
  ]
}
