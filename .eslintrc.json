{
  "root": true,
  "ignorePatterns": [
    "**/node_modules/**",
    "src/vendor/**/**.ts",
    "src/vendor/**/**.html"
  ],
  "overrides": [
    {
      "files": [
        "*.ts"
      ],
      "parser": "@typescript-eslint/parser",
      "parserOptions": {
        "project": [
          "tsconfig.json",
          "src/tsconfig.app.json",
          "src/tsconfig.spec.json"
        ],
        "createDefaultProgram": true
      },
      "plugins": [
        "@stylistic"
      ],
      "extends": [
        "eslint:recommended",
        "plugin:@typescript-eslint/recommended",
        "plugin:@angular-eslint/recommended",
        "plugin:@angular-eslint/template/process-inline-templates",
        "plugin:import/recommended",
        "plugin:import/typescript"
      ],
      "rules": {
        "@angular-eslint/component-class-suffix": "warn", // "error"
        "@angular-eslint/component-selector": [
          "warn", // "error"
          {
            "type": "element",
            "prefix": "app",
            "style": "kebab-case"
          }
        ],
        "@angular-eslint/directive-class-suffix": "warn", // "error"
        "@angular-eslint/directive-selector": [
          "warn", // "error"
          {
            "type": "attribute",
            "prefix": "app",
            "style": "camelCase"
          }
        ],
        "@angular-eslint/no-conflicting-lifecycle": "error",
        "@angular-eslint/no-host-metadata-property": "error",
        "@angular-eslint/no-input-rename": "error",
        "@angular-eslint/no-inputs-metadata-property": "error",
        "@angular-eslint/no-output-native": "error",
        "@angular-eslint/no-output-on-prefix": "warn", // "error",
        "@angular-eslint/no-output-rename": "error",
        "@angular-eslint/no-outputs-metadata-property": "error",
        "@angular-eslint/use-lifecycle-interface": "warn", // "error"
        "@angular-eslint/use-pipe-transform-interface": "error",
        
        "@typescript-eslint/consistent-type-definitions": "error",
        "@typescript-eslint/dot-notation": "off",
        "@typescript-eslint/explicit-member-accessibility": [
          "off",
          {
            "accessibility": "explicit"
          }
        ],
        "@typescript-eslint/member-ordering": [
          "error",
          {
            "default": [
              "public-static-field",
              "private-static-field",
              "public-instance-field",
              "private-instance-field",
              "public-constructor",
              "private-constructor",
              "public-instance-method",
              "protected-instance-method",
              "private-instance-method"
            ]
          }
        ],
        "@typescript-eslint/no-empty-function": "off",
        "@typescript-eslint/no-empty-interface": "error",
        "@typescript-eslint/no-inferrable-types": "off",
        "@typescript-eslint/no-misused-new": "error",
        "@typescript-eslint/no-non-null-assertion": "error",
        "@typescript-eslint/no-shadow": [
          "error",
          {
            "hoist": "all"
          }
        ],
        "@typescript-eslint/no-unused-expressions": "error",
        "@typescript-eslint/no-var-requires": "off",
        "@typescript-eslint/prefer-function-type": "error",
        "@typescript-eslint/no-require-imports": "warn", // "error"
        "@stylistic/quotes": [
          "warn", // "error"
          "single",
          {
            "avoidEscape": true
          }
        ],
        "@stylistic/semi": [
          "warn", // "error"
          "always"
        ],
        "@stylistic/type-annotation-spacing": "error",
        "@typescript-eslint/unified-signatures": "error",
        "@typescript-eslint/no-explicit-any": "off", // "error"
        "arrow-body-style": "warn", // "error"
        "@stylistic/brace-style": [
          "error",
          "1tbs"
        ],
        "constructor-super": "error",
        "curly": "off",
        "@stylistic/eol-last": "error",
        "eqeqeq": [
          "error",
          "smart"
        ],
        "guard-for-in": "error",
        "id-blacklist": "off",
        "id-match": "off",
        "import/no-deprecated": "warn", // "error"
        "import/no-unresolved": "warn", // "error"
        "@stylistic/indent": [
          "warn", // "error"
          2
        ],
        "@stylistic/max-len": [
          "error",
          {
            "code": 140
          }
        ],
        "no-bitwise": "warn", // "error"
        "no-caller": "error",
        "no-console": [
          "error",
          {
            "allow": [
              "warn",
              "error"
            ]
          }
        ],
        "no-debugger": "error",
        "no-empty": "off",
        "no-eval": "error",
        "no-fallthrough": "error",
        "no-new-wrappers": "error",
        "no-restricted-imports": [
          "error",
          "rxjs/Rx"
        ],
        "no-throw-literal": "error",
        "@stylistic/no-trailing-spaces": "error",
        "no-undef-init": "error",
        "no-underscore-dangle": "off",
        "no-unused-labels": "error",
        "no-var": "error",
        "prefer-const": "off", // "error"
        "radix": "error",
        "no-prototype-builtins": "warn", // "error"
        "@stylistic/spaced-comment": [
          "warn", // "error"
          "always",
          {
            "markers": [
              "/"
            ]
          }
        ]
      }
    },
    {
      "files": [
        "*.html"
      ],
      "extends": [
        "plugin:@angular-eslint/template/recommended",
        "plugin:@angular-eslint/template/accessibility"
      ],
      "rules": {}
    }
  ]
}
